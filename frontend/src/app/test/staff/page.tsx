import { createClient } from "@/lib/auth/server-exports"
import { redirect } from "next/navigation"

export default async function StaffTestPage() {
  const supabase = await createClient()

  const { data: { session } } = await supabase.auth.getSession()

  const staffRoles = ['partner', 'attorney', 'paralegal', 'staff']

  if (!session || !staffRoles.includes(session.user.user_metadata.role)) {
    redirect('/dashboard')
  }

  return (
    <div className="p-6 bg-blue-100">
      <h1 className="text-2xl font-bold text-blue-800">Staff Test Page</h1>
      <p>Only visible to staff roles</p>
      <pre>{JSON.stringify(session.user, null, 2)}</pre>
    </div>
  )
}
