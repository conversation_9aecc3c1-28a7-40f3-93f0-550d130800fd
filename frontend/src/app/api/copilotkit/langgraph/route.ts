/**
 * CopilotKit LangGraph API Route
 *
 * This file implements the AG-UI HTTP/SSE JSON event stream protocol for CopilotKit,
 * connecting to the FastAPI LangGraph backend. It supports both streaming and non-streaming
 * responses, JWT authentication, and comprehensive error handling.
 *
 * Key features:
 * - AG-UI protocol compatibility with proper event handling
 * - JWT authentication with tenant isolation
 * - Comprehensive error handling with specific error types
 * - Performance monitoring with first token latency measurement
 * - Request tracing with unique request IDs
 */
import { NextRequest, NextResponse } from 'next/server';
import { isAGUIEnabled, getAGUIVersion, isAGUIFeatureEnabled } from '@/lib/features/ag-ui';
import { PerformanceMonitor } from '@/lib/performance/metrics';
import {
  extractJwtFromRequest,
  validateJwt,
  generateCopilotAuthHeader,
  JWTValidationError
} from '@/lib/auth/jwt-utils';
import { errorHandler } from '@/lib/error-handling/error-handler';
import { ErrorType, AGError as IAGError } from '@/lib/error-handling/error-types';
import { AGError } from '@/lib/error-handling/error-classes';
import { withFallbacks } from '@/lib/error-handling/api-fallback-middleware';
import ConnectionMonitor from '@/lib/error-handling/offline-detection';
import { generateThreadId, generateAgentThreadId } from '@/lib/utils/thread-id';

// Configuration for the LangGraph backend
const config = {
  // Base URL for the FastAPI LangGraph backend
  baseUrl: process.env.LANGGRAPH_API_URL || 'http://localhost:8000',
  // Endpoint for CopilotKit integration
  endpoint: '/copilotkit',
  // Default agent to use if none is specified
  defaultAgent: 'echo_agent',
  // Development mode flag
  devMode: process.env.NODE_ENV === 'development',
  // AG-UI specific configuration
  agui: {
    // Whether AG-UI is enabled
    enabled: isAGUIEnabled(),
    // AG-UI version
    version: getAGUIVersion(),
    // AG-UI features
    features: {
      streaming: isAGUIFeatureEnabled('streaming'),
      toolCalls: isAGUIFeatureEnabled('tool_calls'),
      threadIsolation: isAGUIFeatureEnabled('thread_isolation'),
    },
    // Error handling configuration
    errorHandling: {
      // Whether to include detailed error information in responses
      includeDetails: process.env.NODE_ENV === 'development',
      // Default error message for production
      defaultErrorMessage: 'An error occurred while processing your request.',
      // Whether to retry failed requests
      retryFailedRequests: true,
      // Maximum number of retries
      maxRetries: 3,
    },
    // Telemetry configuration
    telemetry: {
      // Whether to collect telemetry
      enabled: true,
      // Whether to include token counts in telemetry
      includeTokenCounts: true,
      // Whether to include request/response bodies in telemetry (dev only)
      includePayloads: process.env.NODE_ENV === 'development',
    },
  },
};

/**
 * Authentication handler for JWT validation and context extraction
 *
 * This function:
 * 1. Extracts and validates the JWT token from the request
 * 2. Extracts user and organization information from the token
 * 3. Generates a deterministic thread ID for tenant isolation
 * 4. Returns authentication headers and context for the backend
 *
 * @param req The Next.js request object
 * @param body The request body (optional, for thread ID generation)
 * @returns Authentication context object or undefined if authentication fails
 */
async function handleAuth(req: NextRequest, body?: any): Promise<{
  authHeader?: string;
  userId?: string;
  orgId?: string;
  threadId?: string;
  isAuthenticated: boolean;
}> {
  try {
    // Extract JWT token from request
    const token = extractJwtFromRequest(req);

    // Default return for unauthenticated requests
    const defaultReturn = {
      isAuthenticated: false,
      threadId: body?.threadId || generateAgentThreadId('anonymous', 'default', body?.agent || 'default')
    };

    // If no token is present, return default (anonymous access if allowed)
    if (!token) {
      console.log('No JWT token found in request');
      return defaultReturn;
    }

    // Validate the JWT token
    const payload = await validateJwt(token);

    // Extract user and organization information
    const userId = payload.sub;
    const orgId = payload.org_id || payload.user_metadata?.organization_id || 'default';

    // Generate a deterministic thread ID for tenant isolation if not provided
    const threadId = body?.threadId || generateAgentThreadId(
      orgId,
      userId,
      body?.agent || 'default'
    );

    // Generate authorization header with context for CopilotKit
    // This passes organization and user information to maintain tenant isolation
    const authHeader = generateCopilotAuthHeader(payload);

    // Return authentication context
    return {
      authHeader,
      userId,
      orgId,
      threadId,
      isAuthenticated: true
    };
  } catch (error) {
    // Log authentication errors but don't expose details in response
    if (error instanceof JWTValidationError) {
      console.error(`JWT validation error: ${error.message}`);
    } else {
      console.error('Authentication error:', error);
    }

    // Return default for auth failures, the handler will return 401 Unauthorized if required
    return {
      isAuthenticated: false,
      threadId: body?.threadId || generateAgentThreadId('anonymous', 'default', body?.agent || 'default')
    };
  }
}

/**
 * POST handler for AG-UI chat API
 *
 * This handler:
 * 1. Authenticates the request and extracts user/org context
 * 2. Ensures proper thread isolation with deterministic thread IDs
 * 3. Forwards the request to the FastAPI backend
 * 4. Handles errors with appropriate status codes and messages
 * 5. Collects performance metrics for monitoring
 */
export const POST = withFallbacks(
  async (req: NextRequest): Promise<NextResponse> => {
    // Generate request ID for performance tracking
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Start tracking performance (only in browser environment)
    const performanceMonitor = typeof window !== 'undefined' ? PerformanceMonitor.getInstance() : null;
    performanceMonitor?.startRequest(requestId);

    try {
      // Check connection status (only in browser environment)
      const connectionMonitor = typeof window !== 'undefined' ? ConnectionMonitor.getInstance() : null;
      if (connectionMonitor && !connectionMonitor.isOnline()) {
        throw errorHandler.handleError(new Error('Network offline'), {
          type: ErrorType.NETWORK_OFFLINE,
          status: 503
        });
      }

      // Parse the request body
      const body = await req.json();

      // Apply authentication and get context
      const authContext = await handleAuth(req, body);

      // Set the agent name (use echo_agent for testing)
      const agentName = body.agent || config.defaultAgent;

      // Prepare headers for the FastAPI request
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
      };

      // Add authentication header if available
      if (authContext.authHeader) {
        headers['Authorization'] = authContext.authHeader;
      }

      // Add endpoint key if available
      const endpointKey = process.env.CPK_ENDPOINT_SECRET;
      if (endpointKey) {
        headers['X-CPK-Endpoint-Key'] = endpointKey;
      }

      // Add telemetry headers
      headers['X-AG-UI-Version'] = config.agui.version;
      headers['X-AG-UI-Client'] = 'nextjs';

      // Construct the URL for the FastAPI backend
      const apiUrl = `${config.baseUrl}${config.endpoint}`;

      // Prepare the request body with thread ID
      const requestBody = {
        ...body,
        agent: agentName,
        stream: false, // Non-streaming request
        threadId: authContext.threadId || body.threadId,
        metadata: {
          ...(body.metadata || {}),
          requestId,
          userId: authContext.userId || 'anonymous',
          orgId: authContext.orgId || 'default',
          isAuthenticated: authContext.isAuthenticated,
        }
      };

      // Log request details if in development mode
      if (config.devMode && config.agui.telemetry.includePayloads) {
        console.log(`[${requestId}] Request to ${apiUrl}:`, {
          headers: Object.fromEntries(
            Object.entries(headers).filter(([key]) => !key.toLowerCase().includes('secret'))
          ),
          body: requestBody
        });
      }

      // Clone the request with our headers and body
      const modifiedReq = new Request(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody),
      });

      // Forward to FastAPI LangGraph backend
      const response = await fetch(modifiedReq);

      // Check for errors
      if (!response.ok) {
        let errorData: any = {};
        let errorMessage = 'Unknown error';

        try {
          // Try to parse error response as JSON
          errorData = await response.json();
          errorMessage = errorData.error || errorData.message || 'Backend error';
        } catch (e) {
          // If not JSON, get as text
          errorMessage = await response.text();
        }

        // Complete performance tracking
        performanceMonitor?.completeRequest(requestId, {
          cacheHit: false,
          tokenCount: 0
        });

        // Create AG-UI compatible error response
        const errorResponse = {
          error: errorMessage,
          type: 'backend_error',
          status: response.status,
          requestId: requestId,
          threadId: authContext.threadId || body.threadId,
          ...(config.agui.errorHandling.includeDetails ? { details: errorData } : {})
        };

        return new NextResponse(JSON.stringify(errorResponse), {
          status: response.status,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Parse the API response
      const data = await response.json();

      // Extract token count if available
      const tokenCount = data.usage?.total_tokens ||
                         data.metadata?.token_count ||
                         0;

      // Complete performance tracking
      performanceMonitor?.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: tokenCount
      });

      // Log response details if in development mode
      if (config.devMode && config.agui.telemetry.includePayloads) {
        console.log(`[${requestId}] Response from ${apiUrl}:`, {
          status: response.status,
          tokenCount: tokenCount,
          threadId: data.threadId || authContext.threadId,
          messageCount: data.messages?.length || 0
        });
      }

      // Return the API response
      return new NextResponse(JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId
        }
      });
    } catch (error) {
      // Complete performance tracking
      performanceMonitor?.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: 0
      });

      // Create AG-UI compatible error response
      const errorResponse = {
        error: config.agui.errorHandling.includeDetails && error instanceof Error
          ? error.message
          : config.agui.errorHandling.defaultErrorMessage,
        type: error instanceof AGError ? error.type : ErrorType.UNKNOWN,
        status: error instanceof AGError ? error.statusCode : 500,
        requestId: requestId,
        ...(config.agui.errorHandling.includeDetails && error instanceof Error ? {
          stack: error.stack,
          name: error.name
        } : {})
      };

      // Return error response
      return new NextResponse(JSON.stringify(errorResponse), {
        status: error instanceof AGError ? error.statusCode : 500,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId
        }
      });
    }
  },
  {
    cacheFallback: true,
    retry: config.agui.errorHandling.retryFailedRequests,
    maxRetries: config.agui.errorHandling.maxRetries,
    transformErrors: true
  }
);

/**
 * GET handler for AG-UI server-sent events (SSE)
 *
 * This handler:
 * 1. Authenticates the request and extracts user/org context
 * 2. Ensures proper thread isolation with deterministic thread IDs
 * 3. Forwards the streaming request to the FastAPI backend
 * 4. Handles errors with appropriate status codes and messages
 * 5. Collects performance metrics including first token latency
 */
export const GET = withFallbacks(
  async (req: NextRequest): Promise<NextResponse> => {
    // Generate request ID for performance tracking
    const requestId = `stream_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    try {
      // Start tracking performance (only in browser environment)
      const performanceMonitor = typeof window !== 'undefined' ? PerformanceMonitor.getInstance() : null;
      performanceMonitor?.startRequest(requestId);

      // Check connection status (only in browser environment)
      const connectionMonitor = typeof window !== 'undefined' ? ConnectionMonitor.getInstance() : null;
      if (connectionMonitor && !connectionMonitor.isOnline()) {
        throw errorHandler.handleError(new Error('Network offline'), {
          type: ErrorType.NETWORK_OFFLINE,
          status: 503
        });
      }

      // Parse the URL search params for streaming configuration
      const { searchParams } = new URL(req.url);
      const bodyStr = searchParams.get('body') || '{}';
      let body;

      try {
        body = JSON.parse(bodyStr);
      } catch (e) {
        throw errorHandler.handleError(new Error('Invalid request body'), {
          type: ErrorType.INVALID_REQUEST,
          status: 400,
          details: 'Could not parse request body as JSON'
        });
      }

      // Apply authentication and get context
      const authContext = await handleAuth(req, body);

      // Set the agent name (use echo_agent for testing)
      const agentName = body.agent || config.defaultAgent;

      // Prepare headers for the FastAPI request
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
      };

      // Add authentication header if available
      if (authContext.authHeader) {
        headers['Authorization'] = authContext.authHeader;
      }

      // Add endpoint key if available
      const endpointKey = process.env.CPK_ENDPOINT_SECRET;
      if (endpointKey) {
        headers['X-CPK-Endpoint-Key'] = endpointKey;
      }

      // Add telemetry headers
      headers['X-AG-UI-Version'] = config.agui.version;
      headers['X-AG-UI-Client'] = 'nextjs';

      // Construct the URL for the FastAPI backend
      const apiUrl = `${config.baseUrl}${config.endpoint}`;

      // Prepare the request body with thread ID
      const requestBody = {
        ...body,
        agent: agentName,
        stream: true, // Streaming request
        threadId: authContext.threadId || body.threadId,
        metadata: {
          ...(body.metadata || {}),
          requestId,
          userId: authContext.userId || 'anonymous',
          orgId: authContext.orgId || 'default',
          isAuthenticated: authContext.isAuthenticated,
        }
      };

      // Log request details if in development mode
      if (config.devMode && config.agui.telemetry.includePayloads) {
        console.log(`[${requestId}] Streaming request to ${apiUrl}:`, {
          headers: Object.fromEntries(
            Object.entries(headers).filter(([key]) => !key.toLowerCase().includes('secret'))
          ),
          body: requestBody
        });
      }

      // Create a request with the authentication headers and body
      const modifiedReq = new Request(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody),
      });

      // Forward to FastAPI LangGraph backend for streaming
      const response = await fetch(modifiedReq);

      // Check for errors in the response
      if (!response.ok) {
        let errorData: any = {};
        let errorMessage = 'Unknown error';

        try {
          // Try to parse error response as JSON
          errorData = await response.json();
          errorMessage = errorData.error || errorData.message || 'Backend error';
        } catch (e) {
          // If not JSON, get as text
          errorMessage = await response.text();
        }

        // Complete performance tracking
        performanceMonitor?.completeRequest(requestId, {
          cacheHit: false,
          tokenCount: 0
        });

        // Create AG-UI compatible error response
        const errorResponse = {
          error: errorMessage,
          type: 'backend_error',
          status: response.status,
          requestId: requestId,
          threadId: authContext.threadId || body.threadId,
          ...(config.agui.errorHandling.includeDetails ? { details: errorData } : {})
        };

        return new NextResponse(JSON.stringify(errorResponse), {
          status: response.status,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Mark the first token arrival
      performanceMonitor?.markFirstToken(requestId);

      // Create a TransformStream to monitor the stream and collect metrics
      const { readable, writable } = new TransformStream();

      // Pipe the response body through our transform stream
      if (response.body) {
        const reader = response.body.getReader();
        const writer = writable.getWriter();

        // Start the streaming process
        streamWithMetrics(reader, writer, requestId).catch(error => {
          console.error(`[${requestId}] Streaming error:`, error);
          performanceMonitor.completeRequest(requestId, {
            cacheHit: false,
            tokenCount: 0
          });
        });
      }

      // Return the streaming response
      return new NextResponse(readable, {
        status: 200,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'X-Request-ID': requestId
        }
      });
    } catch (error) {
      // Complete performance tracking
      performanceMonitor.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: 0
      });

      // Create AG-UI compatible error response
      const errorResponse = {
        error: config.agui.errorHandling.includeDetails && error instanceof Error
          ? error.message
          : config.agui.errorHandling.defaultErrorMessage,
        type: error instanceof AGError ? (error as AGError).type : ErrorType.UNKNOWN,
        status: error instanceof AGError ? (error as AGError).statusCode : 500,
        requestId: requestId,
        ...(config.agui.errorHandling.includeDetails && error instanceof Error ? {
          stack: error.stack,
          name: error.name
        } : {})
      };

      // Return error response
      return new NextResponse(JSON.stringify(errorResponse), {
        status: error instanceof AGError ? (error as AGError).statusCode : 500,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId
        }
      });
    }
  },
  {
    // Fallback options for GET (streaming) requests
    cacheFallback: false, // Can't cache streaming responses easily
    retry: config.agui.errorHandling.retryFailedRequests,
    maxRetries: config.agui.errorHandling.maxRetries,
    transformErrors: true
  }
);

/**
 * Helper function to stream with metrics collection
 *
 * This function:
 * 1. Reads from the response body reader
 * 2. Writes to the transform stream writer
 * 3. Collects metrics on token count and streaming performance
 * 4. Handles stream completion and errors
 */
async function streamWithMetrics(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  writer: WritableStreamDefaultWriter<Uint8Array>,
  requestId: string
): Promise<void> {
  let tokenCount = 0;
  let chunkCount = 0;
  let done = false;

  try {
    while (!done) {
      const { value, done: doneReading } = await reader.read();
      done = doneReading;

      if (done) {
        break;
      }

      // Write the chunk to the output stream
      await writer.write(value);

      // Count chunks and estimate tokens
      chunkCount++;

      // Estimate token count from SSE data
      // This is a rough estimate - each "data:" line typically contains 1-5 tokens
      const chunk = new TextDecoder().decode(value);
      const dataLines = chunk.split('\n').filter(line => line.startsWith('data:'));
      tokenCount += dataLines.length * 2; // Rough estimate

      // Note: updateMetrics method not available in frontend performance monitor
      // Metrics will be recorded when the request completes
    }

    // Complete the writer
    await writer.close();

    // Complete performance tracking
    performanceMonitor.completeRequest(requestId, {
      cacheHit: false,
      tokenCount
    });
  } catch (error) {
    // Handle errors
    console.error(`[${requestId}] Stream processing error:`, error);

    // Try to close the writer
    try {
      await writer.abort(error);
    } catch (e) {
      // Ignore errors when aborting
    }

    // Complete performance tracking with error
    performanceMonitor.completeRequest(requestId, {
      cacheHit: false,
      tokenCount
    });

    // Re-throw the error
    throw error;
  }
}

/**
 * OPTIONS handler for CORS support
 *
 * This handler:
 * 1. Responds to preflight requests with appropriate CORS headers
 * 2. Allows the necessary headers for AG-UI protocol
 * 3. Caches preflight responses for 24 hours
 */
export const OPTIONS = withFallbacks(
  async (req: NextRequest): Promise<NextResponse> => {
    // Generate request ID for logging
    const requestId = `options_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Log the OPTIONS request in development mode
    if (config.devMode) {
      console.log(`[${requestId}] OPTIONS request received`);
    }

    return new NextResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': [
          'Content-Type',
          'Authorization',
          'X-CPK-Endpoint-Key',
          'X-Request-ID',
          'X-AG-UI-Version',
          'X-AG-UI-Client'
        ].join(', '),
        'Access-Control-Expose-Headers': 'X-Request-ID',
        'Access-Control-Max-Age': '86400', // 24 hours
        'X-Request-ID': requestId
      },
    });
  },
  {
    // Simple OPTIONS requests don't need retries or heavy fallbacks
    cacheFallback: false,
    retry: false,
    transformErrors: false
  }
);
