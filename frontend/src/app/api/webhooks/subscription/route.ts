// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// TODO: Define more accurate types based on payment provider (e.g., Stripe) documentation or SDK
interface BaseWebhookData {
  id: string; // Usually the event ID or subscription/invoice ID
  customer: string; // Payment provider's customer ID
}

interface SubscriptionData extends BaseWebhookData {
  status: string;
  // Add other relevant subscription fields
}

interface PaymentData extends BaseWebhookData {
  status: string; // e.g., 'succeeded', 'failed'
  // Add other relevant invoice/payment fields
}

interface TrialEndData extends BaseWebhookData {
  trial_end: number; // Unix timestamp
  // Add other relevant trial fields
}

// Create a Supabase client for the API route with fallback for build time
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  process.env.SUPABASE_SERVICE_KEY || 'placeholder-service-key'
);

/**
 * Handle subscription webhook events
 * This endpoint is designed to receive webhook events from payment providers like Stripe
 */
export async function POST(req: NextRequest) {
  try {
    // Verify webhook signature (implementation depends on the payment provider)
    // For Stripe, you would use stripe.webhooks.constructEvent

    const body = await req.json();

    // Process the webhook event
    const { type, data } = body;

    // Handle different event types
    switch (type) {
      case 'subscription.created':
        await handleSubscriptionCreated(data);
        break;

      case 'subscription.updated':
        await handleSubscriptionUpdated(data);
        break;

      case 'subscription.deleted':
        await handleSubscriptionDeleted(data);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(data);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(data);
        break;

      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(data);
        break;

      default:
        console.log(`Unhandled event type: ${type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error: unknown) {
    console.error('Error processing webhook:', error);
    let errorMessage = 'Error processing webhook';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ error: errorMessage }, { status: 400 });
  }
}

/**
 * Handle subscription created event
 */
async function handleSubscriptionCreated(data: SubscriptionData) {
  try {
    // Get the subscription data from the payment provider
    const { id: paymentProviderSubscriptionId, customer: customerId, status } = data;

    // Find the tenant by customer ID
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants.firms')
      .select('id')
      .eq('payment_provider_customer_id', customerId)
      .single();

    if (tenantError || !tenant) {
      console.error('Tenant not found for customer ID:', customerId);
      return;
    }

    // Update the subscription in the database
    const { error: updateError } = await supabase
      .from('tenants.tenant_subscriptions')
      .update({
        status,
        payment_provider_subscription_id: paymentProviderSubscriptionId,
        updated_at: new Date().toISOString(),
      })
      .eq('tenant_id', tenant.id)
      .eq('payment_provider_subscription_id', paymentProviderSubscriptionId);

    if (updateError) {
      console.error('Error updating subscription:', updateError);
    }

    // Create a notification for the tenant
    // @ts-ignore - NotificationService class
    const notificationService = new NotificationService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('tenants.users')
      .select('id')
      .eq('tenant_id', tenant.id)
      .eq('role', 'admin');

    if (adminError) {
      console.error('Error fetching admin users:', adminError);
      return;
    }

    // Send notifications to all admin users
    for (const user of adminUsers) {
      // Create notification in the database
      await supabase.from('tenants.notifications').insert({
        tenant_id: tenant.id,
        user_id: user.id,
        type: 'info',
        message: 'Your subscription has been created successfully.',
        priority: 'low',
        created_at: new Date().toISOString(),
      });
    }
  } catch (error: unknown) {
    console.error('Error handling subscription created:', error);
    // Optionally log the error to a monitoring service
  }
}

/**
 * Handle subscription updated event
 */
async function handleSubscriptionUpdated(data: SubscriptionData) {
  try {
    // Get the subscription data from the payment provider
    const { id: paymentProviderSubscriptionId, customer: customerId, status } = data;

    // Find the tenant by customer ID
    const { data: tenant, error: tenantError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('id')
      .eq('payment_provider_customer_id', customerId)
      .single();

    if (tenantError || !tenant) {
      console.error('Tenant not found for customer ID:', customerId);
      return;
    }

    // Update the subscription in the database
    const { error: updateError } = await supabase
      .schema('tenants')
      .from('tenant_subscriptions')
      .update({
        status,
        updated_at: new Date().toISOString(),
      })
      .eq('tenant_id', tenant.id)
      .eq('payment_provider_subscription_id', paymentProviderSubscriptionId);

    if (updateError) {
      console.error('Error updating subscription:', updateError);
    }

    // Create a notification for the tenant
    // @ts-ignore - NotificationService class
    const notificationService = new NotificationService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await supabase
      .schema('tenants')
      .from('users')
      .select('id')
      .eq('tenant_id', tenant.id)
      .eq('role', 'admin');

    if (adminError) {
      console.error('Error fetching admin users:', adminError);
      return;
    }

    // Send notifications to all admin users
    for (const user of adminUsers) {
      await notificationService.createNotification(
        tenant.id,
        user.id,
        'info',
        `Your subscription has been updated to ${status}.`,
        'low'
      );
    }
  } catch (error: unknown) {
    console.error('Error handling subscription updated:', error);
    // Optionally log the error to a monitoring service
  }
}

/**
 * Handle subscription deleted event
 */
async function handleSubscriptionDeleted(data: SubscriptionData) {
  try {
    // Get the subscription data from the payment provider
    const { id: paymentProviderSubscriptionId, customer: customerId } = data;

    // Find the tenant by customer ID
    const { data: tenant, error: tenantError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('id')
      .eq('payment_provider_customer_id', customerId)
      .single();

    if (tenantError || !tenant) {
      console.error('Tenant not found for customer ID:', customerId);
      return;
    }

    // Update the subscription in the database
    const { error: updateError } = await supabase
      .schema('tenants')
      .from('tenant_subscriptions')
      .update({
        status: 'canceled',
        canceled_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('tenant_id', tenant.id)
      .eq('payment_provider_subscription_id', paymentProviderSubscriptionId);

    if (updateError) {
      console.error('Error updating subscription:', updateError);
    }

    // Create a notification for the tenant
    // @ts-ignore - NotificationService class
    const notificationService = new NotificationService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await supabase
      .schema('tenants')
      .from('users')
      .select('id')
      .eq('tenant_id', tenant.id)
      .eq('role', 'admin');

    if (adminError) {
      console.error('Error fetching admin users:', adminError);
      return;
    }

    // Send notifications to all admin users
    for (const user of adminUsers) {
      await notificationService.createNotification(
        tenant.id,
        user.id,
        'warning',
        'Your subscription has been canceled.',
        'high'
      );
    }
  } catch (error: unknown) {
    console.error('Error handling subscription deleted:', error);
    // Optionally log the error to a monitoring service
  }
}

/**
 * Handle payment succeeded event
 */
async function handlePaymentSucceeded(data: PaymentData) {
  try {
    // Get the invoice data from the payment provider
    const { subscription: paymentProviderSubscriptionId, customer: customerId, amount_paid } = data;

    // Find the tenant by customer ID
    const { data: tenant, error: tenantError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('id, name')
      .eq('payment_provider_customer_id', customerId)
      .single();

    if (tenantError || !tenant) {
      console.error('Tenant not found for customer ID:', customerId);
      return;
    }

    // Get the subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .schema('tenants')
      .from('tenant_subscriptions')
      .select('id, current_period_end')
      .eq('tenant_id', tenant.id)
      .eq('payment_provider_subscription_id', paymentProviderSubscriptionId)
      .single();

    if (subscriptionError || !subscription) {
      console.error('Subscription not found:', subscriptionError);
      return;
    }

    // Create a notification for the tenant
    // @ts-ignore - NotificationService class
    const notificationService = new NotificationService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await supabase
      .schema('tenants')
      .from('users')
      .select('id, email, name')
      .eq('tenant_id', tenant.id)
      .eq('role', 'admin');

    if (adminError) {
      console.error('Error fetching admin users:', adminError);
      return;
    }

    // Send notifications to all admin users
    for (const user of adminUsers) {
      await notificationService.createNotification(
        tenant.id,
        user.id,
        'subscription_renewed',
        `Your subscription payment of $${(amount_paid / 100).toFixed(2)} was successful.`,
        'low',
        {
          amount: amount_paid / 100,
          nextBillingDate: subscription.current_period_end,
        }
      );

      // Schedule email notification
      // @ts-ignore - NotificationSchedulerService class
      const notificationSchedulerService = new NotificationSchedulerService(supabase);
      await notificationSchedulerService.scheduleNotification(
        tenant.id,
        user.id,
        'subscription_renewed',
        new Date(), // Send immediately
        {
          amount: amount_paid / 100,
          nextBillingDate: subscription.current_period_end,
          email: user.email,
          tenantName: tenant.name,
          userName: user.name || 'Admin',
        }
      );
    }
  } catch (error: unknown) {
    console.error('Error handling payment succeeded:', error);
    // Optionally log the error to a monitoring service
  }
}

/**
 * Handle payment failed event
 */
async function handlePaymentFailed(data: PaymentData) {
  try {
    // Get the invoice data from the payment provider
    const { subscription: paymentProviderSubscriptionId, customer: customerId } = data;

    // Find the tenant by customer ID
    const { data: tenant, error: tenantError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('id, name')
      .eq('payment_provider_customer_id', customerId)
      .single();

    if (tenantError || !tenant) {
      console.error('Tenant not found for customer ID:', customerId);
      return;
    }

    // Update the subscription in the database
    const { error: updateError } = await supabase
      .schema('tenants')
      .from('tenant_subscriptions')
      .update({
        status: 'past_due',
        updated_at: new Date().toISOString(),
      })
      .eq('tenant_id', tenant.id)
      .eq('payment_provider_subscription_id', paymentProviderSubscriptionId);

    if (updateError) {
      console.error('Error updating subscription:', updateError);
    }

    // Create a notification for the tenant
    // @ts-ignore - NotificationService class
    const notificationService = new NotificationService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await supabase
      .schema('tenants')
      .from('users')
      .select('id, email, name')
      .eq('tenant_id', tenant.id)
      .eq('role', 'admin');

    if (adminError) {
      console.error('Error fetching admin users:', adminError);
      return;
    }

    // Send notifications to all admin users
    for (const user of adminUsers) {
      await notificationService.createNotification(
        tenant.id,
        user.id,
        'subscription_payment_failed',
        'Your subscription payment has failed. Please update your payment information to avoid service interruption.',
        'high'
      );

      // Schedule email notification
      // @ts-ignore - NotificationSchedulerService class
      const notificationSchedulerService = new NotificationSchedulerService(supabase);
      await notificationSchedulerService.scheduleNotification(
        tenant.id,
        user.id,
        'subscription_payment_failed',
        new Date(), // Send immediately
        {
          email: user.email,
          tenantName: tenant.name,
          userName: user.name || 'Admin',
        }
      );
    }
  } catch (error: unknown) {
    console.error('Error handling payment failed:', error);
    // Optionally log the error to a monitoring service
  }
}

/**
 * Handle trial will end event
 */
async function handleTrialWillEnd(data: TrialEndData) {
  try {
    // Get the subscription data from the payment provider
    const { id: paymentProviderSubscriptionId, customer: customerId, trial_end } = data;

    // Find the tenant by customer ID
    const { data: tenant, error: tenantError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('id, name')
      .eq('payment_provider_customer_id', customerId)
      .single();

    if (tenantError || !tenant) {
      console.error('Tenant not found for customer ID:', customerId);
      return;
    }

    // Calculate days remaining
    const trialEnd = new Date(trial_end * 1000); // Convert from Unix timestamp
    const now = new Date();
    const daysRemaining = Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // Create a notification for the tenant
    // @ts-ignore - NotificationService class
    const notificationService = new NotificationService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await supabase
      .schema('tenants')
      .from('users')
      .select('id, email, name')
      .eq('tenant_id', tenant.id)
      .eq('role', 'admin');

    if (adminError) {
      console.error('Error fetching admin users:', adminError);
      return;
    }

    // Send notifications to all admin users
    for (const user of adminUsers) {
      await notificationService.createNotification(
        tenant.id,
        user.id,
        'subscription_trial_ending',
        `Your trial will end in ${daysRemaining} day${daysRemaining === 1 ? '' : 's'}. Please add a payment method to continue using the service.`,
        'medium',
        {
          daysRemaining,
          trialEndDate: trialEnd.toISOString(),
        }
      );

      // Schedule email notification
      // @ts-ignore - NotificationSchedulerService class
      const notificationSchedulerService = new NotificationSchedulerService(supabase);
      await notificationSchedulerService.scheduleNotification(
        tenant.id,
        user.id,
        'subscription_trial_ending',
        new Date(), // Send immediately
        {
          daysRemaining,
          trialEndDate: trialEnd.toISOString(),
          email: user.email,
          tenantName: tenant.name,
          userName: user.name || 'Admin',
        }
      );
    }
  } catch (error: unknown) {
    console.error('Error handling trial will end:', error);
    // Optionally log the error to a monitoring service
  }
}
