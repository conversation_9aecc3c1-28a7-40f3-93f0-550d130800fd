'use client';

import React, { useEffect, useState } from "react";
import { useConnectionStatus } from "@/lib/error-handling/use-connection-status";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, WifiOff, RefreshCw } from "lucide-react";

export default function ConnectionStatusBanner() {
  const [mounted, setMounted] = useState(false);
  const status = useConnectionStatus();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything during SSR or before hydration
  if (!mounted) return null;

  if (status === "online") return null;

  let icon = null;
  let title = "";
  let description = "";
  const color = "border-yellow-500 bg-yellow-50 text-yellow-900";

  if (status === "offline") {
    icon = <WifiOff className="h-5 w-5 text-yellow-600" />;
    title = "You are offline";
    description = "Some features may not be available. Trying to reconnect...";
  } else if (status === "reconnecting") {
    icon = <RefreshCw className="h-5 w-5 animate-spin text-yellow-600" />;
    title = "Reconnecting...";
    description = "Attempting to restore your connection.";
  }

  return (
    <div className="fixed top-0 left-0 w-full z-50">
      <Alert className={`rounded-none ${color} border-2 shadow-md flex items-center`}>
        <div className="mr-3">{icon}</div>
        <div>
          <AlertTitle>{title}</AlertTitle>
          <AlertDescription>{description}</AlertDescription>
        </div>
      </Alert>
    </div>
  );
}
