import React, { useState, useRef, useCallback } from 'react';
import { CopilotChat } from '@copilotkit/react-ui';
import { CopilotErrorBoundary } from './error-boundary';
import { FallbackChat, ChatMessageSkeleton } from '../ui/fallback-chat';
import { useConnectionStatus } from '@/lib/error-handling/use-connection-status';
import { useErrorHandler } from '@/lib/error-handling/error-handler';
import { useFallbackCache } from '@/lib/error-handling/local-cache-fallback';
import { ErrorType, ErrorSeverity, AGError } from '@/lib/error-handling/error-types';
import { toast } from 'sonner';

// Define our own CopilotChatProps interface based on the current API
interface CopilotChatProps {
  className?: string;
  children?: React.ReactNode;
  displayName?: string;
  placeholder?: string;
  suggestions?: string[];
  context?: Record<string, unknown>;
  initialMessage?: string;
  onError?: (error: Error) => void;
  agent?: unknown;
  title?: string;
  showAvatars?: boolean;
  onChatMessage?: (message: any) => Promise<void>;
  onChatStarted?: () => void;
  onChatCompleted?: () => void;
  onChatError?: (error: any) => void;
}

// Extend CopilotChatProps to ensure we capture all possible props
interface ResilientChatProps extends Omit<CopilotChatProps, 'onError'> {
  // Additional props specific to ResilientChat
  className?: string;
  // Add custom event handlers
  onError?: (error: any) => void;
  chatflowId?: string;
  threadId?: string;
  labels?: {
    title?: string;
    initial?: string;
    placeholder?: string;
  };
  instructions?: string;
}

/**
 * A resilient wrapper around CopilotKit's CopilotChat component
 * that provides error handling, fallbacks, and offline support
 */
export function ResilientChat({
  instructions,
  labels = {
    title: "AI Assistant",
    initial: "How can I help you today?",
    placeholder: "Type your message here..."
  },
  className,
  chatflowId,
  threadId,
  ...props
}: ResilientChatProps) {
  const [error, setError] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [messageHistory, setMessageHistory] = useState<any[]>([]);
  const connectionStatus = useConnectionStatus();
  const { handleError } = useErrorHandler();
  const resetKey = useRef(Date.now());

  // Use fallback cache for chat history
  const chatCache = useFallbackCache({
    namespace: 'chat-history',
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  });

  // Cache chat history when new messages come in
  const handleNewMessage = useCallback(async (message: any) => {
    try {
      // Add to message history
      const newHistory = [...messageHistory, message];
      setMessageHistory(newHistory);
      
      // Cache for offline fallback
      if (threadId) {
        await chatCache.set(`thread:${threadId}`, newHistory);
      }
    } catch (error) {
      console.error("Error caching chat message:", error);
    }
  }, [messageHistory, threadId, chatCache]);

  // Load cached history on init
  React.useEffect(() => {
    if (threadId) {
      const loadHistory = async () => {
        try {
          const cached = await chatCache.get(`thread:${threadId}`);
          if (cached && Array.isArray(cached)) {
            setMessageHistory(cached);
          }
        } catch (error) {
          console.error("Error loading chat history:", error);
        }
      };
      
      loadHistory();
    }
  }, [threadId, chatCache]);

  // Handle retry action
  const handleRetry = useCallback(() => {
    setError(null);
    setIsLoading(false);
    toast.info("Retrying connection...");
  }, []);

  // Handle reset action
  const handleReset = useCallback(() => {
    setError(null);
    setMessageHistory([]);
    setIsLoading(false);
    resetKey.current = Date.now();
    toast.info("Starting new conversation");
    
    // Clear cached history if we have a threadId
    if (threadId) {
      chatCache.remove(`thread:${threadId}`);
    }
  }, [threadId, chatCache]);

  // Handle streaming start
  const handleStreamingStart = useCallback(() => {
    setIsLoading(true);
    setError(null);
  }, []);

  // Handle streaming end
  const handleStreamingEnd = useCallback(() => {
    setIsLoading(false);
  }, []);

  // Handle streaming error
  const handleStreamingError = useCallback((err: any) => {
    setIsLoading(false);
    const processedError = handleError(err);
    setError(processedError);
    toast.error(`Chat error: ${processedError.message}`);
  }, [handleError]);

  // If we're offline, show offline message
  if (connectionStatus !== 'online') {
    return (
      <FallbackChat
        error={{
          type: ErrorType.NETWORK_OFFLINE,
          message: connectionStatus === 'reconnecting' 
            ? "Reconnecting to the server..." 
            : "You are offline. Some features may be unavailable.",
          severity: ErrorSeverity.WARNING,
          retryable: connectionStatus === 'reconnecting',
          timestamp: Date.now()
        } as AGError}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <div key={resetKey.current} className={className}>
      <CopilotErrorBoundary
        fallbackUI={(reset) => (
          <FallbackChat
            error={error}
            isLoading={isLoading}
            onRetry={reset}
            onReset={handleReset}
          />
        )}
      >
        {/* Loading skeleton while initializing */}
        {isLoading && messageHistory.length === 0 && (
          <div className="mb-4">
            <ChatMessageSkeleton />
          </div>
        )}
        
        <CopilotChat
          initialMessage={labels.initial || "How can I help you today?"}
          placeholder={labels.placeholder || "Type your message here..."}
          title={labels.title || "AI Assistant"}
          className={className}
          {...props}
        />
      </CopilotErrorBoundary>
    </div>
  );
}
