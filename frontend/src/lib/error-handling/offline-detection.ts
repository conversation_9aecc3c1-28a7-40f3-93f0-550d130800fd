/**
 * Offline detection utility for AG-UI integration
 * Provides network connectivity monitoring and notifications
 */

export type ConnectionStatus = 'online' | 'offline' | 'reconnecting';

// Singleton connection monitor
class ConnectionMonitor {
  private static instance: ConnectionMonitor;
  private status: ConnectionStatus;
  private listeners: Array<(status: ConnectionStatus) => void> = [];
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000; // ms
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  private isServerSide: boolean;

  private constructor() {
    // Detect if we're running on the server
    this.isServerSide = typeof window === 'undefined';

    // Set initial status based on environment
    if (this.isServerSide) {
      // On server, assume online (API routes should work)
      this.status = 'online';
    } else {
      // On client, check navigator.onLine
      this.status = typeof navigator !== 'undefined' && navigator.onLine ? 'online' : 'offline';

      // Only add event listeners in browser environment
      window.addEventListener('online', this.handleOnline);
      window.addEventListener('offline', this.handleOffline);
    }
  }

  public static getInstance(): ConnectionMonitor {
    if (!ConnectionMonitor.instance) {
      ConnectionMonitor.instance = new ConnectionMonitor();
    }
    return ConnectionMonitor.instance;
  }

  public getStatus(): ConnectionStatus {
    return this.status;
  }

  public isOnline(): boolean {
    // On server-side, always return true for API routes to work
    if (this.isServerSide) {
      return true;
    }
    return this.status === 'online';
  }

  public subscribe(callback: (status: ConnectionStatus) => void): () => void {
    this.listeners.push(callback);
    callback(this.status);
    return () => {
      this.listeners = this.listeners.filter(l => l !== callback);
    };
  }

  private notifyListeners = () => {
    this.listeners.forEach(listener => listener(this.status));
  };

  private handleOnline = () => {
    if (this.status === 'offline' || this.status === 'reconnecting') {
      this.status = 'online';
      this.reconnectAttempts = 0;
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      this.notifyListeners();
    }
  };

  private handleOffline = () => {
    if (this.status !== 'offline') {
      this.status = 'offline';
      this.notifyListeners();
      this.attemptReconnect();
    }
  };

  private attemptReconnect = () => {
    if (this.status === 'online' || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }
    this.status = 'reconnecting';
    this.notifyListeners();
    this.reconnectAttempts++;
    this.pingServer()
      .then(isConnected => {
        if (isConnected) {
          this.handleOnline();
        } else if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectTimer = setTimeout(this.attemptReconnect, this.reconnectInterval);
        } else {
          this.status = 'offline';
          this.notifyListeners();
        }
      })
      .catch(() => {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectTimer = setTimeout(this.attemptReconnect, this.reconnectInterval);
        } else {
          this.status = 'offline';
          this.notifyListeners();
        }
      });
  };

  private async pingServer(): Promise<boolean> {
    // Skip ping on server-side
    if (this.isServerSide) {
      return true;
    }

    try {
      const controller = new AbortController();
      const timeout = setTimeout(() => controller.abort(), 2000);
      const response = await fetch('/api/health', { method: 'HEAD', signal: controller.signal });
      clearTimeout(timeout);
      return response.ok;
    } catch {
      return false;
    }
  }

  public checkConnectivity(): void {
    // Skip connectivity check on server-side
    if (this.isServerSide) {
      return;
    }

    this.pingServer()
      .then(isConnected => {
        this.status = isConnected ? 'online' : 'offline';
        this.notifyListeners();
        if (!isConnected) {
          this.attemptReconnect();
        }
      });
  }
}

// Note: React hook moved to separate file to avoid 'use client' conflicts with API routes

export default ConnectionMonitor;
